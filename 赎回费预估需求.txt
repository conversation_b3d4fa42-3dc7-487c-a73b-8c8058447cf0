赎回费预估
根据客户号、基金代码、赎回的银行卡、赎回份额进行校验赎回份额不允许大于该卡该基金的可赎回份额，满足校验后，进行赎回费预估的计算。
获取该客户号、该基金代码下所有未确认的申请成功的赎回申请订单以及当前这笔待落单的订单按照申请时间顺序排序，根据基金代码获取最新的05明细文件，（为了保证一定有05明细文件，中后台要比前端提前一天上线）
对05明细数据，补充不收费的字段，按照业务来源=分红，且基金基本信息中【分红免收赎回费】=是。
对于非循环锁定产品，按照【注册日期从小到大>收费的在前不收费的在后】排序，逐笔订单计算使用的05明细数据，得到{订单、05明细、使用份额，是否不收费}的列表
对于循环锁定产品，将份额锁定结束日<=该笔订单的预计上报日期的明细，按照【注册日期从小到大>收费的在前不收费的在后】排序，逐笔订单计算使用的05明细数据，得到{订单、05明细、使用份额，是否不收费}的列表。
计算当前该笔预计落单的赎回使用的每笔05明细的持有期限和费率值
如果列表的【是否不收费】=是，则该条记录无需计算持有期限，直接费率值为0，
否则，
1）确定持有开始日期、持有结束日期
根据基金代码+业务类型查询【持有时间计算方式】（如果找不到精准的规则，则用通用配置），，
持有开始日期
如果业务类型=认购，则持有开始日期=成立日
如果业务类型=申购，且【持有时间计算方式】的【计算方式】=按申请，则持有开始日期=上报日
如果业务类型=申购，且【持有时间计算方式】的【计算方式】=按确认，则持有开始日期=登记日
如果业务类型=其他，持有开始日期=登记日
持有结束日期
 如果【持有时间计算方式】的【计算方式】=按申请，当前这笔待落单的订单，如果是预约赎回的，取当前工作日所属的赎回预约日期中最近的一个开放日。如果是非预约赎回的，取当前工作日。
如果【持有时间计算方式】的【计算方式】=按确认，在上报日的计算基础上+确认天数
2）根据基金+客户类型+赎回业务类型查询【基金费率配置】确定取费率方式
3）计算持有时间
 【取费率方式】=【按天】，
逐笔计算订单要使用的05明细的持有期限=持有的结束日期-05明细的注册日期
【取费率方式】=【按月】，（月份对月份，日期对日期，如果月份+日期得到的日子该月份没有，就用该月份的最后一天）

 逐笔计算订单要使用的05明细的持有期限，
如果持有的结束日期YYYYMMDD的日DD是该月的最后一天，持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数
如果持有的结束日期YYYYMMDD的日DD不是该月的最后一天，
如果持有的结束日期YYYYMMDD的日DD>=05明细注册日期YYYYMMDD的日DD，则持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数
如果持有的结束日期YYYYMMDD的日DD<05明细注册日期YYYYMMDD的日DD，则持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数-1个月
eg：
持有结束日=20250229，05明细的注册日期=20250101，持有期限=202502-202501的月份=1
持有结束日=20250229，05明细的注册日期=20250131，持有期限=202502-202501的月份=1
持有结束日=20250228，05明细的注册日期=20250101，持有期限=202502-202501的月份=1
持有结束日=20250228，05明细的注册日期=20250131，持有期限=202502-202501的月份-1个月=0
持有结束日=20250301，05明细的注册日期=20250201，持有期限=202503-202502的月份=1
持有结束日=20250328，05明细的注册日期=20250228，持有期限=202503-202502的月份=1
持有结束日=20250327，05明细的注册日期=20250228，持有期限=202503-202502的月份-1个月=0
【取费率方式】=【按月对月底】，

 逐笔计算订单要使用的05明细的持有期限，
如果持有的结束日期YYYYMMDD的日DD是该月的最后一天，持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数
否则，
05明细的注册日期YYYYMMDD的日DD是该月的最后一天，持有持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数-1个月
05明细的注册日期YYYYMMDD的日DD不是该月的最后一天，
如果持有的结束日期YYYYMMDD的日DD>=05明细注册日期YYYYMMDD的日DD，则持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数
如果持有的结束日期YYYYMMDD的日DD<05明细注册日期YYYYMMDD的日DD，则持有期限=持有的结束日期YYYYMMDD的年月YYYYMM-05明细的注册日期YYYYMMDD的年月YYYYMM的月数-1个月
eg：
持有结束日=20250201，05明细的注册日期=20250101，持有期限=202502-202501的月份=1
持有结束日=20250227，05明细的注册日期=20250131，持有期限=202502-202501的月份-1个月=0
持有结束日=20250228，05明细的注册日期=20250131，持有期限=202502-202501的月份=1
持有结束日=20250314，05明细的注册日期=20250215，持有期限=202503-202502的月份-1个月=0
持有结束日=20250315，05明细的注册日期=20250215，持有期限=202503-202502的月份=1
持有结束日=20250328，05明细的注册日期=20250228，持有期限=202503-202502的月份-1个月=0
持有结束日=20250329，05明细的注册日期=20250228，持有期限=202503-202502的月份-1个月=0
持有结束日=20250330，05明细的注册日期=20250228，持有期限=202503-202502的月份-1个月=0
持有结束日=20250331，05明细的注册日期=20250228，持有期限=202503-202502的月份=0
【取费率方式】=【按持有后退出开放日】，
逐笔计算订单要使用的05明细的持有期限=赎回预约结束日>=05明细注册日期的最近的一个日历，和当前工作日所属的赎回预约日历之间间隔的赎回日历个数，含头不含尾，其中剔除临开的日历。
4）根据天数/月数/持有后退出周期数下限<=持有期限<天数/月数/持有后退出周期数上限，得到费率值。
将当前这笔待落单的订单的以下信息返回给前端的字段
字段	说明
取费率方式	
该基金的费率配置中赎回的取费率方式

按天、按月、按月对月底、按持有后退出周期数

是否临开赎回	
产品如果是支持预约赎回的产品，待落单的订单所属的预约日历如果是临开，返回【是临开】的标志

临开情况下，可能按照费率规则计算费用，也可能不按照费率

预估赎回费	sum(使用该笔05明细的份额*最新净值*对应的费率值），向上进位保留两位小数。
以下支持列表
05明细可用份额	可用份额
05明细的业务来源	业务来源
使用该笔05明细的份额	
不收赎回费标记	当业务来源=分红时，取产品的【分红免收赎回费】
对应的持有期限下限	
对应的持有期限上限	
对应的费率值	eg：0.05000000
