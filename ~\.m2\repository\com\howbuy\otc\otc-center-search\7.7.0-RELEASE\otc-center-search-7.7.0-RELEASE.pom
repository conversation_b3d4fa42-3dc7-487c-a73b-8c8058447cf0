<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.otc</groupId>
	<artifactId>otc-center-search</artifactId>
	<version>7.7.0-RELEASE</version>
	<packaging>pom</packaging>

	<properties>
		<project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>
        <com.howbuy.otc-center-search.version>7.7.0-RELEASE</com.howbuy.otc-center-search.version>
        <com.howbuy.otc-common-dao.version>7.7.0-RELEASE</com.howbuy.otc-common-dao.version>
		<com.howbuy.otc-common-service.version>7.7.0-RELEASE</com.howbuy.otc-common-service.version>
		<com.howbuy.otc-outservice.version>7.7.0-RELEASE</com.howbuy.otc-outservice.version>
		<com.howbuy.otc-commons.version>7.7.0-RELEASE</com.howbuy.otc-commons.version>
        <com.howbuy.otc-common.version>7.7.0-RELEASE</com.howbuy.otc-common.version>
		<powermock.version>2.0.2</powermock.version>
		<com.howbuy.otc-component.version>7.7.0-RELEASE</com.howbuy.otc-component.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>2.3.12.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF8</encoding>
				</configuration>
			</plugin>


		</plugins>
	</build>
	</project>