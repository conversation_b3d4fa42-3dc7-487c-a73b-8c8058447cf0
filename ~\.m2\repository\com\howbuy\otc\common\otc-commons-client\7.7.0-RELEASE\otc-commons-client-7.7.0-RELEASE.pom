<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
   <modelVersion>4.0.0</modelVersion>
    <parent>
      <groupId>com.howbuy.otc.common</groupId>
      <artifactId>otc-commons</artifactId>
      <version>7.7.0-RELEASE</version>
    </parent>
    <artifactId>otc-commons-client</artifactId>
    <packaging>jar</packaging>
    <dependencies>
		<dependency>
			<artifactId>howbuy-commons-validator</artifactId>
			<groupId>com.howbuy.commons.validator</groupId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
</project>