<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.otc</groupId>
		<artifactId>otc-center-search</artifactId>
		<version>7.7.0-RELEASE</version>
	</parent>
	<artifactId>otc-center-search-client</artifactId>
	<dependencies>
		<dependency>
			<groupId>com.howbuy.otc.common</groupId>
			<artifactId>otc-commons-client</artifactId>
			<version>${com.howbuy.otc-commons.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-common-api</artifactId>
			<version>${com.howbuy.otc-common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-common-entity</artifactId>
			<version>${com.howbuy.otc-common.version}</version>
		</dependency>
	</dependencies>
	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>Nexus Releases Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>Nexus Snapshots Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>
</project>